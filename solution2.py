"""
Nim游戏问题

题目：
- 有n堆石子，第i堆有ai个石子
- 小C和小Z轮流操作，每次可以从一堆中取走至少1个石子
- 当没有石子可取时，当前玩家败北
- 小C可以进行m次修改，每次修改第pi堆的石子数量为ki个
- 需要判断在初始状态以及每次修改后，小C是否能获胜（小C先手）

解题思路：
这是经典的Nim游戏问题。根据Nim游戏定理：
- 当所有堆的石子数量的异或和为0时，先手玩家必败
- 当异或和不为0时，先手玩家必胜

算法：
1. 计算初始状态的异或和
2. 对于每次修改，更新对应堆的石子数量，重新计算异或和
3. 根据异或和判断胜负
"""

def solve():
    # 读取输入
    n, m = map(int, input().split())
    a = list(map(int, input().split()))
    
    # 计算初始异或和
    xor_sum = 0
    for stones in a:
        xor_sum ^= stones
    
    # 输出初始状态的结果
    if xor_sum != 0:
        print("win")
    else:
        print("lose")
    
    # 处理每次修改
    for _ in range(m):
        p, k = map(int, input().split())
        p -= 1  # 转换为0索引
        
        # 更新异或和：先移除原来的值，再加入新的值
        xor_sum ^= a[p]  # 移除原来第p堆的贡献
        a[p] = k         # 更新第p堆的石子数量
        xor_sum ^= a[p]  # 加入新的第p堆的贡献
        
        # 判断胜负
        if xor_sum != 0:
            print("win")
        else:
            print("lose")

if __name__ == "__main__":
    solve()
