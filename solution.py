"""
<PERSON>bong给定一个长度为n的数组{a1, a2, ..., an}，
你必须将每个元素放入一个子序列中（即把原数组划分为若干子序列），
使得所有子序列的mex之和最大。

解题思路：
1. mex(集合S) = 不在S中的最小非负整数
2. 要使mex之和最大，应该贪心地构造尽可能高mex值的子序列
3. 要构造mex值为k的子序列，需要包含0,1,2,...,k-1，且不包含k
4. 统计每个数字的出现次数，然后贪心地构造子序列

算法：
- 统计每个数字的出现次数
- 重复以下过程直到无法继续：
  - 找到当前能构造的最大mex值（连续从0开始的最长序列）
  - 计算能构造多少个这样的子序列（取决于瓶颈数字的个数）
  - 构造这些子序列并更新计数
"""

def solve():
    T = int(input())

    for _ in range(T):
        n = int(input())
        a = list(map(int, input().split()))

        # 统计每个数字的出现次数
        count = [0] * (n + 2)  # 多留一些空间防止越界
        for num in a:
            if num < len(count):
                count[num] += 1

        total_mex = 0

        # 贪心策略：重复构造当前能构造的最高mex值的子序列
        while True:
            # 找到当前能构造的最大mex值
            # mex值为k意味着需要0,1,2,...,k-1都存在，且k不存在或已用完
            max_possible_mex = 0
            while max_possible_mex < len(count) and count[max_possible_mex] > 0:
                max_possible_mex += 1

            if max_possible_mex == 0:
                # 没有0，无法构造任何有效的子序列
                break

            # 计算能构造多少个mex值为max_possible_mex的子序列
            # 瓶颈是0,1,2,...,max_possible_mex-1中出现次数最少的数字
            min_count = float('inf')
            for i in range(max_possible_mex):
                min_count = min(min_count, count[i])

            if min_count == 0:
                break

            # 构造min_count个mex值为max_possible_mex的子序列
            total_mex += max_possible_mex * min_count

            # 消耗这些数字
            for i in range(max_possible_mex):
                count[i] -= min_count

        print(total_mex)

if __name__ == "__main__":
    solve()
